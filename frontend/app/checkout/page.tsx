'use client';

import React, {useEffect, useRef} from 'react';
const useState = React.useState;
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import ESIMReadyScreen from '../components/ESIMReadyScreen';
import CountryFlag from '../components/CountryFlag';
import { useUser } from '@/context/UserContext';
import SocialAuthButtons from '@/components/SocialAuthButtons';
import PaymentMethods from '@/app/components/PaymentMethods';
import StripePaymentElement, { PaymentFormRef } from '@/app/components/StripePaymentElement';
import StickyPaymentButton from '@/app/components/StickyPaymentButton';
import { generateGuestId, deleteCookie } from '@/utils/cookieUtils';
import { CardForm } from '@/app/components/PaymentMethods';
import { createCheckoutOrder } from '@/utils/checkoutUtils';

interface PackageInfo { // FIXME: DataPackage AND PackageInfo same
    id: number;
    country: string;
    countryCode: string;
    data: string;
    type: string;
    validity: string;
    code: string;
    price: number;
}

interface CheckoutState {
    packageInfo: PackageInfo;
    isLoggedIn: boolean;
    paymentMethod: string;
    cardForm: {
        name: string;
        cardNumber: string;
        expiration: string;
        cvv: string;
    };
    cardErrors: Record<string, string>;
    promoCode: string;
    discount: number;
    showPromoInput: boolean;
    isProcessing: boolean;
    isPaymentSuccess: boolean;
    showSuccess: boolean;
    paymentAmount: number; // FIXME: Remove this
    customerName: string;
    guestId: string;
    step: string; // 'review', 'payment', 'complete'
    showCardForm: boolean; // Kart formunun gösterilip gösterilmeyeceğini kontrol eder
    showLoginPrompt: boolean; // Giriş uyarısının gösterilip gösterilmeyeceğini kontrol eder
    clientSecret: string | null;
}

const CheckoutPage = () => {
    const router = useRouter();
    const { t, i18n } = useTranslation();
    const { user } = useUser();

    // Stripe payment form ref
    const paymentFormRef = useRef<PaymentFormRef>(null);

    const [state, setState] = useState<CheckoutState>({
        packageInfo: {} as PackageInfo,
        isLoggedIn: !!user,
        paymentMethod: 'credit_card', // Default olarak kredi kartı seçili
        cardForm: {
            name: user?.name || '',
            cardNumber: '',
            expiration: '',
            cvv: ''
        },
        cardErrors: {},
        promoCode: '',
        discount: 0,
        showPromoInput: false, // Başlangıçta promosyon kodu inputu gizli
        isProcessing: false,
        isPaymentSuccess: false,
        showSuccess: false,
        paymentAmount: 0,
        customerName: user?.name || '',
        guestId: generateGuestId(), // utils'den alıyoruz
        step: 'review', // Başlangıçta sipariş özeti adımını gösteriyoruz
        showCardForm: false, // Kart formu başlangıçta gizli
        showLoginPrompt: false, // Giriş uyarısı başlangıçta gizli
        clientSecret: null,
    });

    // Destructuring state
    const {
        packageInfo,
        isLoggedIn,
        paymentMethod,
        cardForm,
        cardErrors,
        promoCode,
        discount,
        showPromoInput,
        isProcessing,
        isPaymentSuccess,
        showSuccess,
        paymentAmount,
        customerName,
        showCardForm,
        showLoginPrompt
    } = state;

    // Paket bilgisini localStorage'dan al
    const getPackageInfo = (countryCode: string, packageId: string): PackageInfo => {
        // localStorage'dan paket bilgisini kontrol et
        const storedPackage = localStorage.getItem('checkout_package');

        if (storedPackage) {
            try {
                const packageData = JSON.parse(storedPackage);
                // packageData içinde doğrudan paket bilgileri var mı kontrol et
                if (packageData.packageData) {
                    console.log('Using package data from localStorage:', packageData.packageData);
                    return packageData.packageData;
                }
            } catch (e) {
                console.error('localStorage paket bilgileri işlenemedi:', e);
                // Hata durumunda localStorage'daki veriyi temizle
                localStorage.removeItem('checkout_package');
            }
        }

        // Eğer localStorage'da paket bilgisi yoksa, API'dan çekilmesi gerekir
        // Burada normalde API'ya istek yapılacak
        console.log(`Paket bilgisi için API'ya istek yapılmalı: Ülke=${countryCode}, Paket ID=${packageId}`);

        // Boş obje dön - API entegrasyonu tamamlanana kadar
        return {} as PackageInfo;
    };

    // URL parametrelerini ve localStorage'dan paket bilgisini al
    useEffect(() => {
        const searchParams = new URLSearchParams(window.location.search);
        const countryCodeParam = searchParams.get('countryCode');
        const packageParam = searchParams.get('package');

        // Önce URL parametrelerini kontrol et
        if (countryCodeParam && packageParam) {
            // API'dan paket bilgilerini çek
            // Burada normalde API'ya istek yapılacak, şimdilik statik veri kullanıyoruz
            const packageInfo = getPackageInfo(countryCodeParam.toUpperCase(), packageParam);
            setState((prev: CheckoutState) => ({
                ...prev,
                packageInfo: packageInfo
            }));
        } else {
            // URL parametreleri yoksa localStorage'dan kontrol et
            const storedPackage = localStorage.getItem('checkout_package');
            if (storedPackage) {
                try {
                    const packageData = JSON.parse(storedPackage);
                    // Yeni yapıda her zaman packageData içinde paket bilgileri olacak
                    if (packageData.packageData) {
                        // Doğrudan paket verisi varsa onu kullan
                        setState((prev: CheckoutState) => ({
                            ...prev,
                            packageInfo: packageData.packageData,
                            guestId: packageData.sessionId || state.guestId
                        }));
                    } else if (packageData.countryCode && packageData.packageId) {
                        // Geriye dönük uyumluluk için eski format kontrolü
                        // Burada normalde API'ya istek yapılacak, şimdilik statik veri kullanıyoruz
                        const packageInfo = getPackageInfo(packageData.countryCode, packageData.packageId.toString());
                        setState((prev: CheckoutState) => ({
                            ...prev,
                            packageInfo: packageInfo,
                            // SessionId'yi de state'e ekle
                            guestId: packageData.sessionId || state.guestId
                        }));
                    }
                } catch (e) {
                    console.error('localStorage paket bilgileri işlenemedi:', e);
                    // Hata durumunda localStorage'daki veriyi temizle
                    localStorage.removeItem('checkout_package');
                }
            } else {
                // Paket bilgisi yoksa paketler sayfasına yönlendir
                console.error('Paket bilgisi bulunamadı');
                router.push('/packages');
            }
        }
    }, [router]);

    useEffect(() => {
        const createOrder = async () => {
            if (user && packageInfo.code) {
                try {
                    const clientSecret = await createCheckoutOrder(
                        packageInfo.code,
                        promoCode || null
                    );

                    setState(prev => ({
                        ...prev,
                        clientSecret
                    }));
                } catch (error) {
                    console.error('Error creating checkout order:', error);
                }
            }
        };

        createOrder();
    }, [user, packageInfo.code, promoCode]);

    // Kullanıcı durumunu güncelle
    useEffect(() => {
        if (user) {

            setState(prev => ({
                ...prev,
                isLoggedIn: true,
                customerName: user.name || prev.customerName,
                cardForm: {
                    ...prev.cardForm,
                    name: user.name || prev.cardForm.name
                },
                // Kullanıcı giriş yaptığında guest ID'yi koruyoruz
                guestId: state.guestId
            }));
        } else {
            setState(prev => ({
                ...prev,
                isLoggedIn: false
            }));
        }
    }, [user]);

    // Ödeme yöntemleri artık PaymentMethods bileşenine taşındı

    // Kart form validasyonu
    const validateCardField = (name: string, value: string) => {
        let error = '';

        switch (name) {
            case 'name':
                if (value.trim() === '') {
                    error = t('checkout.cardErrors.nameRequired');
                }
                break;
            case 'cardNumber':
                if (!/^\d*$/.test(value)) {
                    error = t('checkout.cardErrors.numbersOnly');
                } else if (value.replace(/\s/g, '').length > 16) {
                    error = t('checkout.cardErrors.maxDigits');
                } else if (value.replace(/\s/g, '').length < 16 && value.length > 0) {
                    error = t('checkout.cardErrors.exactDigits');
                }
                break;
            case 'expiration':
                if (!/^\d*\/?(\d{0,2})?$/.test(value)) {
                    error = t('checkout.cardErrors.invalidFormat');
                }
                break;
            case 'cvv':
                if (!/^\d*$/.test(value)) {
                    error = t('checkout.cardErrors.numbersOnly');
                } else if (value.length > 0 && (value.length < 3 || value.length > 4)) {
                    error = t('checkout.cardErrors.cvvLength');
                }
                break;
        }

        return error;
    };

    // Kart form işleyicileri
    const handleCardFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        let processedValue = value;

        // Özel işleme kuralları
        if (name === 'cardNumber') {
            // Sadece rakamları al
            processedValue = value.replace(/\D/g, '');

            // 16 rakamdan fazlasını kesme
            if (processedValue.length > 16) {
                processedValue = processedValue.slice(0, 16);
            }

            // 4'lü gruplar halinde formatlama
            processedValue = processedValue.replace(/(\d{4})(?=\d)/g, '$1 ');
        } else if (name === 'expiration') {
            // Yalnızca sayı ve / karakterlerine izin ver
            processedValue = value.replace(/[^\d\/]/g, '');

            // Otomatik / ekleme
            if (processedValue.length === 2 && !processedValue.includes('/') && state.cardForm.expiration.length === 1) {
                processedValue += '/';
            }

            // 5 karakterden (AA/YY) fazlasını kes
            if (processedValue.length > 5) {
                processedValue = processedValue.slice(0, 5);
            }
        } else if (name === 'cvv') {
            // Sadece rakamları al
            processedValue = value.replace(/\D/g, '');

            // En fazla 4 rakama izin ver
            if (processedValue.length > 4) {
                processedValue = processedValue.slice(0, 4);
            }
        }

        // Form güncelleme
        setState((prev: CheckoutState) => ({
            ...prev,
            cardForm: {
                ...prev.cardForm,
                [name]: processedValue
            }
        }));

        // Validasyon
        const error = validateCardField(name, processedValue);
        setState((prev: CheckoutState) => ({
            ...prev,
            cardErrors: {
                ...prev.cardErrors,
                [name]: error
            }
        }));
    };

    // Promosyon kodu işleyicisi
    const applyPromoCode = () => {
        // Gerçek uygulamada API'a istek yapılacak
        if (state.promoCode === 'ESIM10') {
            setState((prev: CheckoutState) => ({
                ...prev,
                discount: state.packageInfo.price * 0.1, // %10 indirim
                promoCode: ''
            }));
            alert('Promosyon kodu uygulandı! %10 indirim kazandınız.');
        } else if (state.promoCode === 'ESIM20') {
            setState((prev: CheckoutState) => ({
                ...prev,
                discount: state.packageInfo.price * 0.2, // %20 indirim
                promoCode: ''
            }));
            alert('Promosyon kodu uygulandı! %20 indirim kazandınız.');
        } else {
            alert('Geçersiz promosyon kodu!');
            setState((prev: CheckoutState) => ({
                ...prev,
                discount: 0,
                promoCode: ''
            }));
        }
    };

    // Not: handleSocialAuth fonksiyonu artık SocialAuthButtons bileşenine taşındı

    // Ödeme işlemi
    const handlePay = async () => {
        // Kart ödeme yöntemi seçildiyse validasyon yap
        if (state.paymentMethod === 'credit_card') {
            const errors: { [key: string]: string } = {};

            Object.keys(state.cardForm).forEach(key => {
                const error = validateCardField(key, state.cardForm[key as keyof CardForm]);
                if (error) {
                    errors[key] = error;
                }
            });

            if (Object.keys(errors).length > 0) {
                setState((prev: CheckoutState) => ({
                    ...prev,
                    cardErrors: errors
                }));
                return;
            }
        }

        // İşlem başlıyor - processing ekranını göster
        setState((prev: CheckoutState) => ({
            ...prev,
            isProcessing: true
        }));

        try {
            // Backend'e ödeme isteği gönder
            // Önerilen checkout objesi formatına uygun veri hazırla
            const checkoutData = {
                user_id: user?.id || null,
                guest_id: !user ? state.guestId : null,
                cart_items: [{
                    package_id: state.packageInfo.id,
                    country_code: state.packageInfo.countryCode,
                    price: state.packageInfo.price,
                    discount: state.discount,
                    quantity: 1
                }],
                shipping_info: null, // Bu örnekte shipping bilgisi yok
                payment_info: state.paymentMethod === "credit_card" ? {
                    method: state.paymentMethod,
                    // Hassas kart bilgilerini direkt olarak göndermek yerine,
                    // gerçek uygulamada bir token alıp onu gönderebilirsiniz
                    token: 'payment_token_placeholder'
                } : {
                    method: state.paymentMethod
                },
                step: state.step,
                promo_code: state.promoCode || null
            };

            // Simulate API call - gerçek uygulamada burada API isteği olacak
            console.log('Checkout data:', checkoutData);
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Get the selected package price for the ESIMReadyScreen
            const selectedPackagePrice = state.packageInfo.price - state.discount;
            setState((prev: CheckoutState) => ({
                ...prev,
                paymentAmount: selectedPackagePrice,
                customerName: state.cardForm.name.split(' ')[0]
            }));

            // Ödeme başarılı olduğunda localStorage'daki paket bilgilerini temizle
            localStorage.removeItem('checkout_package');
            console.log('Checkout package data cleared from localStorage after successful payment');

            setState((prev: CheckoutState) => ({
                ...prev,
                isProcessing: false,
                isPaymentSuccess: true,
                showSuccess: true
            }));
        } catch (error) {
            setState((prev: CheckoutState) => ({
                ...prev,
                isProcessing: false,
                isPaymentSuccess: false
            }));
            alert('Ödeme işlemi sırasında bir hata oluştu.');
        }
    };

    // Ödeme yöntemi seçildi mi kontrol eder
    const isPaymentMethodSelected = () => {
        return state.paymentMethod !== '';
    };

    // Seçilen ödeme yönteminin adını döndürür
    const getPaymentMethodName = (methodId: string) => {
        switch (methodId) {
            case 'credit_card':
                return t('checkout.paymentMethods.creditCard');
            case 'applepay':
                return t('checkout.paymentMethods.applePay');
            case 'googlepay':
                return t('checkout.paymentMethods.googlePay');
            default:
                return '';
        }
    };

    // Promosyon kodu input gösterme/gizleme fonksiyonu
    const togglePromoCodeInput = () => {
        setState((prev: CheckoutState) => ({
            ...prev,
            showPromoInput: !prev.showPromoInput
        }));
    };

    // eSIM'lerim sayfasına yönlendirme
    const navigateToEsims = () => {
        router.push('/esims');
    };

    // Payment method selected handler güncellemesi
    const handlePaymentMethodSelect = (methodId: string) => {
        if (methodId === 'completed') {
            // Ödeme tamamlandı, success ekranını göster
            setState(prev => ({
                ...prev,
                isPaymentSuccess: true,
                showSuccess: true
            }));
            return;
        }

        setState(prev => ({
            ...prev,
            paymentMethod: methodId,
            showCardForm: false
        }));
    };

    // Focus stilleri
    const inputFocusStyle = {
        focusOutline: 'none',
        focusBorderWidth: '3px',
        transition: 'border-width 0.2s ease'
    };

    // Add CSS for highlight effect
    React.useEffect(() => {
        // Add a style tag for the highlight animation
        const style = document.createElement('style');
        style.innerHTML = `
            @keyframes highlightPulse {
                0% { box-shadow: 0 0 0 0 rgba(250, 204, 21, 0.4); }
                70% { box-shadow: 0 0 0 10px rgba(250, 204, 21, 0); }
                100% { box-shadow: 0 0 0 0 rgba(250, 204, 21, 0); }
            }
            .highlight-section {
                animation: highlightPulse 1.5s ease-out;
                background-color: rgba(209, 213, 219, 0.5);
                border-radius: 0.5rem;
            }
        `;
        document.head.appendChild(style);

        return () => {
            document.head.removeChild(style);
        };
    }, []);

    if (showSuccess) {
        return (
            <div className="fixed inset-0 bg-white z-50">
                <ESIMReadyScreen />
            </div>
        );
    }

    // Paket bilgisi yoksa yükleniyor göster
    if (!state.packageInfo || !state.packageInfo.id) {
        return (
            <div className="app-container pb-24 pt-6 relative flex items-center justify-center">
                <div className="bg-white rounded-3xl p-8 w-full max-w-sm flex flex-col items-center">
                    <div className="mb-6">
                        <svg className="animate-spin h-12 w-12 text-yellow-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </div>
                    <h2 className="text-xl font-bold text-center mb-2">{t('checkout.loading')}</h2>
                    <p className="text-gray-600 text-center">{t('checkout.loadingPackage')}</p>
                </div>
            </div>
        );
    }

    const translatedCountryName = t(
        `regions:countries.${state.packageInfo.countryCode.toUpperCase()}.name`
    );

    function showPaymentFormLoading() {
        return <div className="flex flex-col items-center justify-center py-8">
            <div className="mb-4">
                <svg className="animate-spin h-8 w-8 text-yellow-500" xmlns="http://www.w3.org/2000/svg" fill="none"
                     viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                            strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            </div>
            <p className="text-gray-600">{t('checkout.loading')}</p>
        </div>;
    }

    return (
        <div className="app-container pb-24 pt-6 relative">
            {/* Processing Overlay */}
            {isProcessing && (
                <div className="fixed inset-0 bg-black bg-opacity-70 z-50 flex flex-col items-center justify-center">
                    <div className="bg-white rounded-3xl p-8 w-full max-w-sm flex flex-col items-center">
                        <div className="mb-6">
                            <svg className="animate-spin h-12 w-12 text-yellow-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        </div>
                        <h2 className="text-xl font-bold text-center mb-2">{t('checkout.processing.title')}</h2>
                        <p className="text-gray-600 text-center">{t('checkout.processing.wait')}</p>
                    </div>
                </div>
            )}



            {/* Header */}
            <div className="flex items-center mb-6">
                <Link href={`/${i18n.language}/${translatedCountryName.toLowerCase()}-esim`} className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-200 hover:bg-gray-300 transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                         stroke="#4B5563" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M19 12H5"></path>
                        <polyline points="12 19 5 12 12 5"></polyline>
                    </svg>
                </Link>
                <h1 className="text-2xl font-bold flex-1 text-center">
                    {state.step === 'review' ? t('checkout.packageDetails.title') : t('checkout.payment')}
                </h1>
            </div>


            {/* Package Details */}
            <div className="mb-6">
            <h2 className="text-lg font-semibold mb-3">{t('checkout.packageDetails.title')}</h2>
                <div className="bg-white rounded-3xl p-4">
                    <div className="flex items-center mb-3">
                        <div className="ml-1 mr-4 w-8 h-8">
                            <CountryFlag
                                countryCode={state.packageInfo.countryCode}
                                width={32}
                                alt={state.packageInfo.country}
                            />
                        </div>
                        <span className="font-medium text-lg">{translatedCountryName}</span>
                    </div>

                    <div className="mb-4">
                        <div className="flex justify-between py-2 border-b border-gray-100">
                            <span className="text-gray-600">{t('checkout.packageDetails.package')}</span>
                            <span className="font-medium">{state.packageInfo.data} / {state.packageInfo.validity} {t('packages.days', 'days')}</span>
                        </div>
                    </div>

                    <div className="bg-gray-100 p-3 rounded-2xl">
                        {state.discount > 0 && (
                            <div className="flex justify-between items-center mb-2 text-green-600">
                                <span>{t('checkout.discount')}</span>
                                <span>-${state.discount.toFixed(2)}</span>
                            </div>
                        )}

                        <div className="flex justify-between items-center text-lg font-bold">
                            <span>{t('checkout.total')}</span>
                            <span>${(state.packageInfo.price - state.discount).toFixed(2)}</span>
                        </div>

                        {/* Promo Code Link */}
                    </div>

                    <div className="flex justify-start mt-3">
                        <div
                            className="cursor-pointer flex items-center"
                            onClick={togglePromoCodeInput}
                        >
                            <span
                                className="text-black font-medium border-b border-black mr-1 text-sm">{t('checkout.promoCode.show')}</span>
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="14"
                                height="14"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                className={`transform ${state.showPromoInput ? 'rotate-180' : ''}`}
                            >
                                <polyline points="6 9 12 15 18 9"></polyline>
                            </svg>
                        </div>
                    </div>

                    {state.showPromoInput && (
                        <div className="flex space-x-2 mt-3">
                            <input
                                type="text"
                                value={state.promoCode}
                                onChange={(e) => setState((prev: CheckoutState) => ({
                                    ...prev,
                                    promoCode: e.target.value.toUpperCase()
                                }))}
                                placeholder={t('checkout.promoCode.placeholder')}
                                className="flex-1 border border-gray-300 rounded-3xl px-3 py-2 focus:outline-none focus:border-yellow-400 focus:border-3 transition-all"
                                style={{
                                    transition: inputFocusStyle.transition
                                }}
                            />
                            <button
                                onClick={applyPromoCode}
                                className="bg-yellow-400 hover:bg-yellow-500 text-black px-4 py-2 rounded-3xl"
                            >
                                {t('checkout.promoCode.apply')}
                            </button>
                        </div>
                    )}
                </div>
            </div>


            {isLoggedIn && (
                <div className="mb-6 stripe-payment-section">
                    <h2 className="text-lg font-semibold mb-3">{t('checkout.payment')}</h2>
                    <div className="bg-white rounded-3xl p-4">
                        {!state.clientSecret ? showPaymentFormLoading() : (
                            <StripePaymentElement
                                ref={paymentFormRef}
                                clientSecret={state.clientSecret}
                                onPaymentSuccess={() => {
                                    setState(prev => ({
                                        ...prev,
                                        isPaymentSuccess: true,
                                        showSuccess: true
                                    }));
                                }}
                                onPaymentError={(error) => {
                                    console.error('Payment failed:', error);
                                    setState(prev => ({
                                        ...prev,
                                        isProcessing: false,
                                        isPaymentSuccess: false
                                    }));
                                    alert(t('checkout.payment.error'));
                                }}
                                onProcessingChange={(isProcessing) => {
                                    setState(prev => ({
                                        ...prev,
                                        isProcessing
                                    }));
                                }}
                            />
                        )}
                    </div>
                </div>
            )}

            {!isLoggedIn && (
                <PaymentMethods
                    paymentMethod={state.paymentMethod}
                    cardForm={state.cardForm}
                    cardErrors={state.cardErrors}
                    inputFocusStyle={inputFocusStyle}
                    onPaymentMethodSelect={handlePaymentMethodSelect}
                    onCardFormChange={handleCardFormChange}
                    validateCardField={validateCardField}
                    showCardForm={state.showCardForm}
                    isLoggedIn={state.isLoggedIn}
                    clientSecret={state.clientSecret}
                />)}

            {/* Login or Register Section - Show after payment methods */}
            {!isLoggedIn && (
                <div className="mb-6 login-section">
                    <h2 className="text-lg font-semibold mb-3">{t('checkout.authentication')}</h2>
                    <div className="bg-white rounded-3xl p-4">
                        {/* Show login message when payment method is selected and showLoginPrompt is true */}
                        {showLoginPrompt && (
                            <div
                                className="flex items-center p-4 mb-3 bg-gray-100 rounded-3xl login-warning">
                                <div className="flex-shrink-0 mr-4">
                                    <div
                                        className="flex items-center justify-center w-8 h-8 bg-yellow-400 rounded-full">
                                        <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5"
                                             viewBox="0 0 24 24" fill="none">
                                            <circle cx="12" cy="12" r="10" fill="#FACC15"/>
                                            <path d="M12 2V13" stroke="#000000" strokeWidth={2.5}
                                                  strokeLinecap="round"/>
                                            <path d="M12 19.5H12.01" stroke="#000000" strokeWidth={4}
                                                  strokeLinecap="round" strokeLinejoin="round"/>
                                        </svg>
                                    </div>
                                </div>
                                <span
                                    className="text-gray-800 font-medium">{t('checkout.loginToPayment')}.</span>
                            </div>
                        )}

                        {/* SocialAuthButtons bileşenini kullan */}
                        <SocialAuthButtons
                            checkoutData={{
                                countryCode: state.packageInfo.countryCode,
                                packageId: state.packageInfo.id,
                                sessionId: state.guestId,
                                step: state.step,
                                productCode: state.packageInfo.code,
                                // Paket verilerini de ekleyelim
                                packageData: state.packageInfo
                            }}
                        />
                    </div>
                </div>
            )}

            {/* Sticky Payment Button */}
            <StickyPaymentButton
                isLoggedIn={isLoggedIn}
                paymentMethod={state.paymentMethod}
                showCardForm={state.showCardForm}
                totalAmount={state.packageInfo.price - state.discount}
                isPaymentMethodSelected={isPaymentMethodSelected()}
                paymentFormRef={paymentFormRef}
                onPaymentClick={handlePay}
                onShowCardForm={() => {
                    setState(prev => ({ ...prev, showCardForm: true }));
                }}
                onShowLoginPrompt={() => {
                    setState(prev => ({ ...prev, showLoginPrompt: true }));
                }}
                getPaymentMethodName={getPaymentMethodName}
            />

        </div>
    );
};

export default CheckoutPage;
